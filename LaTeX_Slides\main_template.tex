\documentclass[aspectratio=169]{beamer}

% Required packages
\usepackage{xeCJK}
\usepackage{tikz}
\usepackage{listings}
\usepackage{booktabs}
\usepackage{graphicx}
\usepackage{hyperref}
\usepackage{fontawesome5}
\usepackage{colortbl}
\usepackage{multicol}
\usepackage{array}

% Set Chinese fonts
\setCJKmainfont{Microsoft YaHei}
\setCJKsansfont{Microsoft YaHei}
\setCJKmonofont{Microsoft YaHei}

% Color theme - Blue gradient
\definecolor{primary}{RGB}{30,144,255}
\definecolor{secondary}{RGB}{70,130,180}
\definecolor{accent}{RGB}{135,206,250}
\definecolor{dark}{RGB}{25,25,112}
\definecolor{light}{RGB}{240,248,255}

% Beamer theme configuration
\usetheme{Madrid}
\usecolortheme[named=primary]{structure}

% Custom colors for theme
\setbeamercolor{palette primary}{bg=primary,fg=white}
\setbeamercolor{palette secondary}{bg=secondary,fg=white}
\setbeamercolor{palette tertiary}{bg=accent,fg=dark}
\setbeamercolor{palette quaternary}{bg=dark,fg=white}

\setbeamercolor{titlelike}{bg=primary,fg=white}
\setbeamercolor{frametitle}{bg=primary,fg=white}
\setbeamercolor{item}{fg=primary}
\setbeamercolor{block title}{bg=secondary,fg=white}
\setbeamercolor{block body}{bg=light,fg=dark}

% Remove navigation symbols
\setbeamertemplate{navigation symbols}{}

% Custom title page
\setbeamertemplate{title page}{
  \vbox{}
  \vfill
  \begingroup
    \centering
    \begin{beamercolorbox}[sep=8pt,center,colsep=-4bp,rounded=true]{title}
      \usebeamerfont{title}\inserttitle\par%
      \ifx\insertsubtitle\@empty%
      \else%
        \vskip0.25em%
        {\usebeamerfont{subtitle}\usebeamercolor[fg]{subtitle}\insertsubtitle\par}%
      \fi%     
    \end{beamercolorbox}%
    \vskip1em\par
    \begin{beamercolorbox}[sep=8pt,center,colsep=-4bp,rounded=true]{author}
      \usebeamerfont{author}\insertauthor
    \end{beamercolorbox}
    \begin{beamercolorbox}[sep=8pt,center,colsep=-4bp,rounded=true]{institute}
      \usebeamerfont{institute}\insertinstitute
    \end{beamercolorbox}
    \begin{beamercolorbox}[sep=8pt,center,colsep=-4bp,rounded=true]{date}
      \usebeamerfont{date}\insertdate
    \end{beamercolorbox}\vskip0.5em
  \endgroup
  \vfill
}

% Custom fonts
\setbeamerfont{title}{size=\LARGE,series=\bfseries}
\setbeamerfont{subtitle}{size=\large}
\setbeamerfont{frametitle}{size=\Large,series=\bfseries}
\setbeamerfont{block title}{size=\normalsize,series=\bfseries}

% Code listing settings
\lstset{
    basicstyle=\ttfamily\small,
    keywordstyle=\color{primary}\bfseries,
    commentstyle=\color{secondary}\itshape,
    stringstyle=\color{accent},
    numbers=left,
    numberstyle=\tiny\color{gray},
    frame=single,
    breaklines=true,
    backgroundcolor=\color{light}
}

% Custom commands for consistency
\newcommand{\highlight}[1]{\textcolor{primary}{\textbf{#1}}}
\newcommand{\keyword}[1]{\textcolor{secondary}{\textbf{#1}}}
\newcommand{\concept}[1]{\textcolor{accent}{\textit{#1}}}

% TikZ settings for diagrams
\usetikzlibrary{shapes,arrows,positioning,decorations.pathreplacing}
\tikzstyle{box} = [rectangle, rounded corners, minimum width=3cm, minimum height=1cm, text centered, draw=primary, fill=light]
\tikzstyle{arrow} = [thick,->,>=stealth,color=primary]

% Header and footer
\setbeamertemplate{headline}{
  \begin{beamercolorbox}[ht=2.5ex,dp=1.125ex,leftskip=.3cm,rightskip=.3cm plus1fil]{section in head/foot}
    \usebeamerfont{section in head/foot}\insertsectionhead
  \end{beamercolorbox}
}

\setbeamertemplate{footline}{
  \begin{beamercolorbox}[ht=2.5ex,dp=1.125ex,leftskip=.3cm,rightskip=.3cm plus1fil]{author in head/foot}
    \usebeamerfont{author in head/foot}\insertshortauthor\hfill\insertframenumber/\inserttotalframenumber
  \end{beamercolorbox}
}

% Block environments
\setbeamertemplate{blocks}[rounded][shadow=true]

% Itemize settings
\setbeamertemplate{itemize items}[circle]
\setbeamertemplate{enumerate items}[circle]

% Table settings
\renewcommand{\arraystretch}{1.2}

% Title slide information (to be customized per week)
\title{AI驱动传媒内容制作}
\subtitle{第X周：课程标题}
\author{授课教师}
\institute{汕头大学}
\date{\today}